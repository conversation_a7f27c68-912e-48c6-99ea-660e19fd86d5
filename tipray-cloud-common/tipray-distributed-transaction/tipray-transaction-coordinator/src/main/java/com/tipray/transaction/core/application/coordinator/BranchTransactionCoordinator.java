package com.tipray.transaction.core.application.coordinator;

import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import com.tipray.transaction.core.application.extension.branch.BranchTransactionExtensionManager;
import com.tipray.transaction.core.application.handler.mode.TransactionModeHandler;
import com.tipray.transaction.core.application.handler.mode.TransationModeHandlerFactory;
import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.coordinator.BranchTransactionInfo;
import com.tipray.transaction.core.domain.coordinator.TransactionCoordinator;
import com.tipray.transaction.core.domain.statemachine.branch.BranchTransactionStateMachine;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.BranchTransationConfig;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.enums.BranchTransactionEvent;
import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionPropagation;
import com.tipray.transaction.core.exception.DistributedBranchTransactionException;
import com.tipray.transaction.core.exception.DistributedTransactionCommitException;
import com.tipray.transaction.core.exception.DistributedTransactionException;
import com.tipray.transaction.core.exception.DistributedTransactionSystemException;
import com.tipray.transaction.core.infrastructure.async.AsyncTransactionExecutor;
import com.tipray.transaction.core.infrastructure.event.TransactionEventPublisherImpl;
import com.tipray.transaction.core.infrastructure.metrics.TransactionMetricsCollector;
import com.tipray.transaction.core.util.LoggingUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import javax.sql.DataSource;
import java.sql.Connection;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 分支事务协调器
 * 处理与远程服务的分支事务协调（保留务实方案价值）
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Slf4j
public class BranchTransactionCoordinator implements TransactionCoordinator {

    private final TransactionEventPublisherImpl eventPublisher;
    private final TransactionMetricsCollector metricsCollector;
    private final TransationModeHandlerFactory transationModeHandlerFactory;
    private final BranchTransactionExtensionManager extensionManager;
    private final AsyncTransactionExecutor asyncExecutor;
    private final PlatformTransactionManager platformTransactionManager;
    private final BranchTransactionStateMachine branchStateMachine;

    // 存储分支事务信息
    private final Map<String, List<BranchTransactionDO>> branchTransactionMap = new ConcurrentHashMap<>();

    // 存储本地事务状态（事务ID -> Spring事务状态）
    private final Map<String, TransactionStatus> localTransactionStatusMap = new ConcurrentHashMap<>();

    public BranchTransactionCoordinator(TransactionEventPublisherImpl eventPublisher,
                                        TransactionMetricsCollector metricsCollector,
                                        TransationModeHandlerFactory transationModeHandlerFactory,
                                        AsyncTransactionExecutor asyncExecutor,
                                        BranchTransactionExtensionManager extensionManager,
                                        PlatformTransactionManager platformTransactionManager,
                                        BranchTransactionStateMachine branchStateMachine) {
        this.eventPublisher = eventPublisher;
        this.metricsCollector = metricsCollector;
        this.transationModeHandlerFactory = transationModeHandlerFactory;
        this.extensionManager = extensionManager;
        this.asyncExecutor = asyncExecutor;
        this.platformTransactionManager = platformTransactionManager;
        this.branchStateMachine = branchStateMachine;
    }

    @Override
    public void begin(TransactionContext context) {
        // 分支事务协调器不需要在begin阶段做特殊处理
        // 分支事务在实际调用远程服务时才注册和执行
    }

    @Override
    public void initialize(TransactionContext context) {
        String transactionId = context.getTransactionId();

        // 初始化分支事务列表
        branchTransactionMap.put(transactionId, new CopyOnWriteArrayList<>());

        // 自动注册本地分支
        registerLocalBranch(context);

        log.debug("[{}|{}] [DEBUG] - 分支事务协调器初始化完成",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId());
    }

    /**
     * 注册分支事务
     */
    public Long registerBranch(TransactionContext context, BranchTransactionDO branchTransactionDO) {
        String transactionId = context.getTransactionId();
        Long branchId = branchTransactionDO.getBranchTransactionId();

        // 显式初始化分支执行状态
        branchStateMachine.initializeBranchStatus(branchId, BranchTransactionStatus.UNKNOWN);

        // 分支注册前拓展
        extensionManager.executeBeforeBranchRegister(context, branchTransactionDO);

        try {
            // 3. 添加到分支事务列表
            List<BranchTransactionDO> branches = branchTransactionMap.get(transactionId);
            if (branches != null) {
                branches.add(branchTransactionDO);
            }

            // 设置分支id到当前上下文中
            TransactionContext currentContext = TransactionContextHolder.getCurrentContext();
            if (currentContext != null) {
                currentContext.setBranchId(branchId);
            }

            // 通过状态机设置分支状态为已注册（自动触发持久化和内存状态更新）
            branchStateMachine.transition(branchId, BranchTransactionEvent.REGISTER, branchTransactionDO);

            // 分支注册后拓展
            extensionManager.executeAfterBranchRegister(context, branchTransactionDO);

            BranchTransactionInfo branchInfo = BranchTransactionInfo.builder()
                    .transactionId(transactionId)
                    .branchId(generateBranchId(branchTransactionDO.getTransactionId(), branchTransactionDO.getBranchTransactionId()))
                    .serviceEndpoint(branchTransactionDO.getTargetService())
                    .serviceMethod(branchTransactionDO.getTargetMethod())
                    .requestData(branchTransactionDO.getResult())
                    .status(BranchTransactionStatus.REGISTERED)
                    .registrationTime(System.currentTimeMillis())
                    .build();

            // 4. 发布事件
            eventPublisher.publishBranchTransactionRegistered(branchInfo);

            // 5. 记录指标
            metricsCollector.recordBranchTransactionRegistration(branchInfo);

            log.info("[{}|{}] [REGISTER] - 分支注册 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("service", branchTransactionDO.getTargetService(),
                                             "method", branchTransactionDO.getTargetMethod()));

            return branchId;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 分支事务注册失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            branchStateMachine.transitionWithFailure(branchId, BranchTransactionEvent.REGISTER_FAILURE,
                    branchTransactionDO,
                    e, "分支注册失败: " + e.getMessage());
            throw new DistributedTransactionException("分支事务注册失败", e);
        }
    }

    @Override
    public void commit(TransactionContext context) {
        String transactionId = context.getTransactionId();

        List<BranchTransactionDO> branches = branchTransactionMap.get(transactionId);
        if (branches == null || branches.isEmpty()) {
            log.debug("[{}|{}] [DEBUG] - 无分支事务需要提交",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId());
            return;
        }

        // 分离本地分支和远程分支
        BranchTransactionDO localBranch = null;
        List<BranchTransactionDO> remoteBranches = new ArrayList<>();

        for (BranchTransactionDO branch : branches) {
            if (branch.isLocalBranch()) {
                localBranch = branch;
            } else if (branch.isRemoteBranch()) {
                remoteBranches.add(branch);
            }
        }

        // 筛选出需要提交的远程分支
        Stream<BranchTransactionDO> needCommitRemoteBranches = remoteBranches.stream()
                .filter(branch -> branch.getStatus() == BranchTransactionStatus.EXECUTED);

        try {
            // 是否需要异步提交远程分支
            if (context.isAsyncCommitOrRollback()) {
                // 使用专门的异步执行器进行提交
                List<Runnable> commitTasks = needCommitRemoteBranches
                        .map(branch -> (Runnable) () -> commitBranch(branch, context.getMode()))
                        .collect(Collectors.toList());

                AsyncTransactionExecutor.AsyncExecutionResult result =
                        asyncExecutor.executeBatchCommitAsync(commitTasks, context.getTimeout());

                if (result.hasFailures()) {
                    throw new DistributedTransactionCommitException(
                            "异步提交失败，失败原因: " + String.join("; ", result.getFailures()),
                            DistributedTransactionCommitException.CommitFailureType.BRANCH_COMMIT_FAILED
                    );
                }

                log.debug("[{}|{}] [DEBUG] - 异步提交完成 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("成功", result.getSuccessCount(), "失败", result.getFailureCount()));
            } else {
                needCommitRemoteBranches.forEach(branch -> commitBranch(branch, context.getMode()));
            }

            // 无论是否异步 都要最后在提交本地分支
            if (localBranch != null) {
                commitLocalBranch(localBranch);
            }

            long duration = System.currentTimeMillis() - System.currentTimeMillis(); // 简化处理
            log.info("[{}|{}] [EXEC] - 分支事务提交成功 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                    LoggingUtils.formatContext("分支数", branches.size(), "cost", duration + "ms"));
        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 分支事务提交失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e), e);
            throw new DistributedBranchTransactionException("分支事务提交失败", e);
        } finally {
            // 清理分支事务信息和本地事务状态
            branchTransactionMap.remove(transactionId);
            localTransactionStatusMap.remove(transactionId);
        }
    }

    /**
     * 提交本地分支
     */
    private void commitLocalBranch(BranchTransactionDO localBranch) {

        if (!localBranch.isLocalBranch()) {
            throw new IllegalArgumentException("本地分支处理器只能处理本地分支");
        }

        String transactionId = localBranch.getTransactionId();
        Long branchId = localBranch.getBranchTransactionId();

        if (platformTransactionManager == null) {
            log.debug("[{}|{}] [DEBUG] - 未配置本地事务管理器，跳过本地分支提交",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId));
            return;
        }

        try {
            // 从状态提供者获取Spring事务状态
            TransactionStatus status = localTransactionStatusMap.get(transactionId);
            if (status != null && !status.isCompleted()) {
                // 通过状态机设置提交状态（自动触发持久化和内存状态更新）
                branchStateMachine.transition(localBranch.getBranchTransactionId(), BranchTransactionEvent.START_COMMIT, localBranch);

                // 提交本地事务
                platformTransactionManager.commit(status);

                // 先进入提交中状态，再进入已提交状态
                branchStateMachine.transition(localBranch.getBranchTransactionId(), BranchTransactionEvent.COMMIT_SUCCESS, localBranch);

                // 发布事件
                eventPublisher.publishLocalBranchCommitted(localBranch);

                // 记录指标
                metricsCollector.recordLocalBranchCommit(localBranch);

                log.info("[{}|{}] [COMMIT] - 本地分支提交成功 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("cost", "0ms"));
            } else {
                log.debug("[{}|{}] [DEBUG] - 本地事务已完成，跳过提交",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId));
            }

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 本地分支提交失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);

            // 提交失败
            branchStateMachine.transitionWithFailure(branchId,
                    BranchTransactionEvent.COMMIT_FAILURE,
                    localBranch,
                    e, "本地分支提交失败: " + e.getMessage());

            // 发布失败事件
            eventPublisher.publishLocalBranchCommitFailed(localBranch, e);

            throw new DistributedTransactionCommitException(
                    "本地分支提交失败，失败原因: " + String.join("; ", e.getMessage()),
                    e,
                    DistributedTransactionCommitException.CommitFailureType.BRANCH_COMMIT_FAILED
            );
        }
    }

    @Override
    public void rollback(TransactionContext context, Exception cause) {

        String transactionId = context.getTransactionId();

        List<BranchTransactionDO> branches = branchTransactionMap.get(transactionId);
        if (branches == null || branches.isEmpty()) {
            log.debug("[{}|{}] [DEBUG] - 无分支事务需要回滚",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId());
            return;
        }

        log.info("[{}|{}] [EXEC] - 开始回滚事务 {}",
                LoggingUtils.getTxId(), null,
                LoggingUtils.formatContext("处理器", context.getMode().getName()));

        // 分离本地分支和远程分支
        BranchTransactionDO localBranch = null;
        List<BranchTransactionDO> remoteBranches = new ArrayList<>();

        for (BranchTransactionDO branch : branches) {
            if (branch.isLocalBranch()) {
                localBranch = branch;
            } else if (branch.isRemoteBranch() &&
                    (branch.getStatus() == BranchTransactionStatus.EXECUTED ||
                            branch.getStatus() == BranchTransactionStatus.EXECUTING)) {
                remoteBranches.add(branch);
            }
        }

        // 逆序回滚远程分支事务
        List<BranchTransactionDO> needRollbackRemoteBranches = remoteBranches.stream()
                .filter(branch -> branch.getStatus() == BranchTransactionStatus.EXECUTED ||
                        branch.getStatus() == BranchTransactionStatus.EXECUTING)
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                            Collections.reverse(list);
                            return list;
                        }
                ));

        try {
            // 是否需要异步回滚
            if (context.isAsyncCommitOrRollback()) {
                // 使用专门的异步执行器进行回滚
                List<Runnable> rollbackTasks = needRollbackRemoteBranches.stream()
                        .map(branch -> (Runnable) () -> {
                            try {
                                rollbackBranch(branch, context.getMode(), cause);
                            } catch (Exception e) {
                                log.error("[{}|{}] [ERROR] - 异步回滚分支事务失败 {}",
                                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branch.getBranchTransactionId()),
                                        LoggingUtils.formatException(e), e);
                                // 回滚失败不抛异常，避免掩盖原始异常，但记录失败信息
                            }
                        })
                        .collect(Collectors.toList());

                AsyncTransactionExecutor.AsyncExecutionResult result =
                        asyncExecutor.executeBatchRollbackAsync(rollbackTasks, context.getTimeout());

                if (result.hasFailures()) {
                    log.error("[{}|{}] [ERROR] - 部分分支回滚失败，需要人工干预 {}",
                            LoggingUtils.getTxId(transactionId), null,
                            LoggingUtils.formatContext("失败详情", result.getFailures().toString()));
                    // TODO: 发送告警或记录到需要人工干预的列表
                }

                log.debug("[{}|{}] [DEBUG] - 异步回滚完成 {}",
                        LoggingUtils.getTxId(transactionId), null,
                        LoggingUtils.formatContext("成功", result.getSuccessCount(), "失败", result.getFailureCount()));
            } else {
                needRollbackRemoteBranches.forEach(branch -> rollbackBranch(branch, context.getMode(), cause));
            }

            // 最后回滚本地分支（确保原子性）
            if (localBranch != null) {
                rollbackLocalBranch(localBranch);
            }

            log.info("[{}|{}] [DEBUG] - 事务回滚成功 {}",
                    LoggingUtils.getTxId(transactionId), null,
                    LoggingUtils.formatContext("分支数", branches.size()));
        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 事务回滚失败 {}",
                    LoggingUtils.getTxId(transactionId), null,
                    LoggingUtils.formatException(e), e);
            // 回滚失败不抛异常，避免掩盖原始异常
            // TODO 回滚失败很严重，看看怎么处理。补偿机制 或者人工处理
        } finally {
            // 清理分支事务信息和本地事务状态
            branchTransactionMap.remove(transactionId);
            localTransactionStatusMap.remove(transactionId);
        }
    }

    /**
     * 回滚本地分支
     */
    private void rollbackLocalBranch(BranchTransactionDO localBranch) {
        if (!localBranch.isLocalBranch()) {
            throw new IllegalArgumentException("本地分支处理器只能处理本地分支");
        }

        String transactionId = localBranch.getTransactionId();
        Long branchId = localBranch.getBranchTransactionId();

        if (platformTransactionManager == null) {
            log.debug("[{}|{}] [DEBUG] - 未配置本地事务管理器，跳过本地分支回滚",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId));
            return;
        }

        try {
            // 从状态提供者获取Spring事务状态
            TransactionStatus status = localTransactionStatusMap.get(transactionId);
            if (status != null && !status.isCompleted()) {

                // 通过状态机设置回滚状态（自动触发持久化和内存状态更新）
                branchStateMachine.transition(localBranch.getBranchTransactionId(), BranchTransactionEvent.START_ROLLBACK, localBranch);

                // 回滚本地事务
                platformTransactionManager.rollback(status);

                // 先进入回滚中状态，再进入已回滚状态
                branchStateMachine.transition(localBranch.getBranchTransactionId(), BranchTransactionEvent.ROLLBACK_SUCCESS, localBranch);

                // 发布事件
                eventPublisher.publishLocalBranchRolledBack(localBranch);

                // 记录指标
                metricsCollector.recordLocalBranchRollback(localBranch);

                log.info("[{}|{}] [ROLLBACK] - 本地分支回滚成功 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("cost", "0ms"));
            } else {
                log.debug("[{}|{}] [DEBUG] - 本地事务已完成，跳过回滚",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId));
            }

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 本地分支回滚失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);
            // 通过状态机设置回滚失败状态（携带失败原因，自动更新内存状态）
            branchStateMachine.transitionWithFailure(localBranch.getBranchTransactionId(),
                    BranchTransactionEvent.ROLLBACK_FAILURE,
                    localBranch,
                    e, "本地分支回滚失败: " + e.getMessage());
            // 本地事务回滚失败不抛异常，避免掩盖原始异常，但记录失败信息
        }
    }

    /**
     * 获取分支事务统计信息
     */
    public BranchTransactionStatistics getStatistics() {
        int totalBranches = branchTransactionMap.values().stream()
                .mapToInt(List::size)
                .sum();

        return BranchTransactionStatistics.builder()
                .activeTransactionCount(branchTransactionMap.size())
                .totalBranchCount(totalBranches)
                .build();
    }

    // ==================== 新增方法 ====================

    /**
     * 注册本地分支
     */
    private void registerLocalBranch(TransactionContext context) {
        String transactionId = context.getTransactionId();

        // 创建本地分支
        BranchTransactionDO localBranch = BranchTransactionDO.createLocalBranch(
                "发起方分支", context.getMethodSignature());
        localBranch.setTransactionId(transactionId);
        localBranch.setStepOrder(0); // 本地分支始终是第一个

        try {
            // 开始本地事务并保存状态到协调器中
            if (platformTransactionManager != null) {
                DefaultTransactionDefinition definition = createTransactionDefinition(context);
                TransactionStatus status = platformTransactionManager.getTransaction(definition);
                localTransactionStatusMap.put(transactionId, status);
            }

            // 注册到分支列表
            registerBranch(context, localBranch);

            log.info("[{}|{}] [REGISTER] - 本地分支注册 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(localBranch.getBranchTransactionId()),
                    LoggingUtils.formatContext("service", "LOCAL", "method", context.getMethodSignature()));

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 本地分支注册失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(localBranch.getBranchTransactionId()),
                    LoggingUtils.formatException(e), e);
            throw new DistributedTransactionException("本地分支注册失败", e);
        }
    }

    /**
     * 创建事务定义
     */
    private DefaultTransactionDefinition createTransactionDefinition(TransactionContext context) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();

        // 设置传播行为
        definition.setPropagationBehavior(mapToPlatformPropagation(context.getPropagation()));

        // 设置隔离级别（使用默认）
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);

        // 设置超时时间
        definition.setTimeout(context.getTimeout() > 0 ? context.getTimeout() : -1);

        // 设置只读属性
        definition.setReadOnly(context.isReadOnly());

        return definition;
    }

    /**
     * 映射传播行为到Spring事务传播行为
     */
    private int mapToPlatformPropagation(TransactionPropagation propagation) {
        switch (propagation) {
            case REQUIRED:
                return TransactionDefinition.PROPAGATION_REQUIRED;
            case REQUIRES_NEW:
                return TransactionDefinition.PROPAGATION_REQUIRES_NEW;
            case SUPPORTS:
                return TransactionDefinition.PROPAGATION_SUPPORTS;
            case MANDATORY:
                return TransactionDefinition.PROPAGATION_MANDATORY;
            case NOT_SUPPORTED:
                return TransactionDefinition.PROPAGATION_NOT_SUPPORTED;
            case NEVER:
                return TransactionDefinition.PROPAGATION_NEVER;
            case NESTED:
                return TransactionDefinition.PROPAGATION_NESTED;
            default:
                return TransactionDefinition.PROPAGATION_REQUIRED;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 提交分支事务
     */
    private void commitBranch(BranchTransactionDO branchTransactionDO, TransactionMode mode) {
        String transactionId = branchTransactionDO.getTransactionId();
        Long branchId = branchTransactionDO.getBranchTransactionId();
        TransactionContext context = TransactionContextHolder.getCurrentContext();

        BranchTransactionInfo branchInfo = null;

        try {


            // 1. 执行分支事务提交前扩展
            extensionManager.executeBeforeBranchCommit(context, branchTransactionDO);

            // 2. 委托事务模式处理器执行
            TransactionModeHandler handler = transationModeHandlerFactory.getHandler(mode);

            // 更新状态 - 提交中
            branchStateMachine.transition(branchId, BranchTransactionEvent.START_COMMIT, branchTransactionDO);

            // 3. 执行提交调用
            handler.commitBranch(branchTransactionDO);

            // 更新状态 - 提交完成
            branchStateMachine.transition(branchId, BranchTransactionEvent.COMMIT_SUCCESS, branchTransactionDO);

            // 4. 执行分支事务提交后扩展
            extensionManager.executeAfterBranchCommit(context, branchTransactionDO, true);

            branchInfo = BranchTransactionInfo.builder()
                    .transactionId(transactionId)
                    .branchId(generateBranchId(branchTransactionDO.getTransactionId(), branchTransactionDO.getBranchTransactionId()))
                    .serviceEndpoint(branchTransactionDO.getTargetService())
                    .serviceMethod(branchTransactionDO.getTargetMethod())
                    .requestData(branchTransactionDO.getResult())
                    .status(BranchTransactionStatus.REGISTERED)
                    .registrationTime(System.currentTimeMillis())
                    .build();

            // 5. 发布事件
            eventPublisher.publishBranchTransactionCommitted(branchInfo);

            log.info("[{}|{}] [COMMIT] - 分支事务提交成功 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("cost", "0ms"));

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 分支事务提交失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);

            // 更新状态 - 提交失败
            branchStateMachine.transitionWithFailure(branchId,
                    BranchTransactionEvent.COMMIT_FAILURE,
                    branchTransactionDO,
                    e, "分支提交失败: " + e.getMessage());

            // 执行分支事务提交后扩展（失败情况）
            extensionManager.executeAfterBranchCommit(context, branchTransactionDO, false);

            // 发布失败事件
            eventPublisher.publishBranchTransactionCommitFailed(branchInfo, e);
        }
    }

    /**
     * 回滚分支事务
     */
    private void rollbackBranch(BranchTransactionDO branchTransactionDO, TransactionMode mode, Exception cause) {
        String transactionId = branchTransactionDO.getTransactionId();
        Long branchId = branchTransactionDO.getBranchTransactionId();
        TransactionContext context = TransactionContextHolder.getCurrentContext();

        BranchTransactionInfo branchInfo = null;

        try {
            // 1. 执行分支事务回滚前扩展
            extensionManager.executeBeforeBranchRollback(context, branchTransactionDO, cause);

            // 2. 委托给事务模式处理器执行
            TransactionModeHandler handler = transationModeHandlerFactory.getHandler(mode);

            // 更新状态 - 回滚中
            branchStateMachine.transition(branchId, BranchTransactionEvent.START_ROLLBACK, branchTransactionDO);

            // 3. 执行回滚调用
            handler.rollbackBranch(branchTransactionDO, cause);

            // 更新状态 - 回滚成功
            branchStateMachine.transition(branchId, BranchTransactionEvent.ROLLBACK_SUCCESS, branchTransactionDO);

            // 4. 执行分支事务回滚后扩展
            extensionManager.executeAfterBranchRollback(context, branchTransactionDO, cause, true);

            branchInfo = BranchTransactionInfo.builder()
                    .transactionId(transactionId)
                    .branchId(generateBranchId(branchTransactionDO.getTransactionId(), branchTransactionDO.getBranchTransactionId()))
                    .serviceEndpoint(branchTransactionDO.getTargetService())
                    .serviceMethod(branchTransactionDO.getTargetMethod())
                    .requestData(branchTransactionDO.getResult())
                    .status(BranchTransactionStatus.REGISTERED)
                    .registrationTime(System.currentTimeMillis())
                    .build();

            // 5. 发布事件
            eventPublisher.publishBranchTransactionRolledBack(branchInfo, cause);

            log.info("[{}|{}] [ROLLBACK] - 分支事务回滚成功 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatContext("cost", "0ms"));

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 分支事务回滚失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);

            // 更新状态 - 回滚失败
            branchStateMachine.transitionWithFailure(branchId,
                    BranchTransactionEvent.ROLLBACK_FAILURE,
                    branchTransactionDO,
                    e, "分支回滚失败: " + e.getMessage());

            // 执行分支事务回滚后扩展（失败情况）
            extensionManager.executeAfterBranchRollback(context, branchTransactionDO, cause, false);

            // 发布失败事件
            eventPublisher.publishBranchTransactionRollbackFailed(branchInfo, e);
        }
    }

    /**
     * 查找分支事务信息
     */
    private BranchTransactionDO findBranchInfo(String transactionId, Long branchId) {
        List<BranchTransactionDO> branches = branchTransactionMap.get(transactionId);
        if (branches == null) {
            return null;
        }

        return branches.stream()
                .filter(branch -> branch.getBranchTransactionId().equals(branchId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 生成分支ID
     */
    private String generateBranchId(String transactionId, Long branchId) {
        return transactionId + "-branch-" + System.currentTimeMillis() + "-" +
                ThreadLocalRandom.current().nextInt(1000);
    }

    /**
     * 执行分支事务
     */
    public Object executeBranch(TransactionContext currentContext, BranchTransactionDO branchTransactionDO, ProceedingJoinPoint joinPoint) {

        // 执行前拓展
        extensionManager.executeBeforeBranchExecute(currentContext, branchTransactionDO, 1);

        // 执行分支业务逻辑
        Object result = executeBranchLogic(currentContext, branchTransactionDO, joinPoint);

        // 执行后拓展
        extensionManager.executeAfterBranchExecute(currentContext, branchTransactionDO, result, true, null);

        return result;
    }

    public BranchTransationConfig parseBranchTransationConfig(DistributedBranchTransaction annotation, Method method, TransactionMode mode) {
        return transationModeHandlerFactory.getHandler(mode).parseBranchTransationConfig(annotation, method, mode);
    }

    public void clearBranchTransactionResource(TransactionMode mode) {
        transationModeHandlerFactory.getHandler(mode).clearBranchTransactionResource();
    }

    // ==================== 私有方法 ====================

    /**
     * 执行分支业务逻辑
     * 统一处理异常和状态更新
     *
     * @param currentContext      当前事务上下文
     * @param branchTransactionDO 分支事务对象
     * @param joinPoint           切点
     * @return 执行结果
     */
    @SneakyThrows
    private Object executeBranchLogic(TransactionContext currentContext,
                                      BranchTransactionDO branchTransactionDO,
                                      ProceedingJoinPoint joinPoint) {
        Long branchId = branchTransactionDO.getBranchTransactionId();
        String transactionId = currentContext.getTransactionId();

        try {
            // 返回的结果
            Object result = null;

            // 委托给具体的执行器执行
            TransactionModeHandler handler = transationModeHandlerFactory.getHandler(currentContext.getMode());

            // 更新状态 - 执行中
            branchStateMachine.transition(branchTransactionDO.getBranchTransactionId(), BranchTransactionEvent.START_EXECUTE, branchTransactionDO);

            if (handler == null) {
                // 其他模式直接执行
                log.debug("[{}|{}] [DEBUG] - 未找到事务模式处理器，直接执行分支逻辑",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId));
                result = proceedJoinPoint(joinPoint);
            } else {
                // 通过执行器执行
                log.debug("[{}|{}] [DEBUG] - 通过事务模式处理器执行分支逻辑 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("模式", currentContext.getMode()));
                result = handler.executeBranch(currentContext, branchTransactionDO,
                        () -> proceedJoinPoint(joinPoint));
            }

            // 状态机转换：EXECUTING -> EXECUTED（执行成功）
            branchStateMachine.transition(branchTransactionDO.getBranchTransactionId(), BranchTransactionEvent.EXECUTE_SUCCESS, branchTransactionDO);

            return result;
        } catch (Exception e) {
            // 状态机转换：EXECUTING -> FAILED（执行失败）
            branchStateMachine.transitionWithFailure(branchId,
                    BranchTransactionEvent.EXECUTE_FAILURE,
                    branchTransactionDO,
                    e, "分支执行失败: " + e.getMessage());

            log.error("[{}|{}] [ERROR] - 分支事务执行失败 {}",
                    LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                    LoggingUtils.formatException(e), e);

            // 根据是否为关键步骤决定是否抛出异常
            if (branchTransactionDO.getCritical() != null && branchTransactionDO.getCritical()) {
                // 重新抛出异常
                throw e;
            } else {
                log.warn("[{}|{}] [WARN] - 非关键步骤执行失败，继续执行 {}",
                        LoggingUtils.getTxId(transactionId), LoggingUtils.getBranchId(branchId),
                        LoggingUtils.formatContext("模式", currentContext.getMode()));
                return null; // 非关键步骤失败时返回null
            }
        }
    }

    /**
     * 执行切点逻辑，统一异常处理
     *
     * @param joinPoint 切点
     * @return 执行结果
     * @throws Exception 执行异常
     */
    private Object proceedJoinPoint(ProceedingJoinPoint joinPoint) throws Exception {
        try {
            return joinPoint.proceed();
        } catch (Throwable e) {
            // 统一异常转换逻辑
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else if (e instanceof Error) {
                throw (Error) e;
            } else if (e instanceof Exception) {
                throw (Exception) e;
            } else {
                // 其他类型的Throwable转换为系统异常
                throw new DistributedTransactionSystemException(
                        "分支事务执行异常: " + e.getMessage(),
                        e,
                        DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                        DistributedTransactionSystemException.SeverityLevel.MEDIUM,
                        "BranchTransaction"
                );
            }
        }
    }

    /**
     * 获取分支事务数量
     *
     * @param transactionId 事务ID
     * @return 分支数量
     */
    public int getBranchCount(String transactionId) {
        List<BranchTransactionDO> branches = branchTransactionMap.get(transactionId);
        return branches != null ? branches.size() : 0;
    }

    /**
     * 分支事务统计信息
     */
    public static class BranchTransactionStatistics {
        private final int activeTransactionCount;
        private final int totalBranchCount;

        private BranchTransactionStatistics(Builder builder) {
            this.activeTransactionCount = builder.activeTransactionCount;
            this.totalBranchCount = builder.totalBranchCount;
        }

        public static Builder builder() {
            return new Builder();
        }

        // getters
        public int getActiveTransactionCount() {
            return activeTransactionCount;
        }

        public int getTotalBranchCount() {
            return totalBranchCount;
        }

        @Override
        public String toString() {
            return String.format("BranchTransactionStatistics{activeCount=%d, totalBranches=%d}",
                    activeTransactionCount, totalBranchCount);
        }

        public static class Builder {
            private int activeTransactionCount;
            private int totalBranchCount;

            public Builder activeTransactionCount(int activeTransactionCount) {
                this.activeTransactionCount = activeTransactionCount;
                return this;
            }

            public Builder totalBranchCount(int totalBranchCount) {
                this.totalBranchCount = totalBranchCount;
                return this;
            }

            public BranchTransactionStatistics build() {
                return new BranchTransactionStatistics(this);
            }
        }
    }


}
