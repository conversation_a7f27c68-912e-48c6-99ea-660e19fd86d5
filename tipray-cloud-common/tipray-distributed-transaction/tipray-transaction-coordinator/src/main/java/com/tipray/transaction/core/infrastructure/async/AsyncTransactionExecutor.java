package com.tipray.transaction.core.infrastructure.async;

import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.infrastructure.pool.TransactionConnectionPoolManager;
import com.tipray.transaction.core.util.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.sql.DataSource;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.Map;

/**
 * 异步事务执行器
 * 通过 TransactionConnectionPoolManager 获取线程池，不再硬编码线程池配置
 *
 * <AUTHOR>
 * @version 3.0
 * @since 2025-01-23
 */
@Slf4j
public class AsyncTransactionExecutor {

    @Autowired
    private TransactionConnectionPoolManager poolManager;

    @Autowired
    private DataSource dataSource;

    // 异步提交线程池（从连接池管理器获取）
    private ExecutorService commitExecutor;

    // 异步回滚线程池（从连接池管理器获取）
    private ExecutorService rollbackExecutor;

    @PostConstruct
    public void init() {
        log.debug("[{}|{}] [DEBUG] - 初始化异步事务执行器",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId());

        // 从连接池管理器获取线程池，不再硬编码配置
        this.commitExecutor = poolManager.getTransactionExecutor();
        this.rollbackExecutor = poolManager.getTransactionExecutor();

        log.debug("[{}|{}] [DEBUG] - 异步事务执行器初始化完成 {}",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                LoggingUtils.formatContext("提交线程池", commitExecutor.getClass().getSimpleName(),
                                         "回滚线程池", rollbackExecutor.getClass().getSimpleName()));
    }

    /**
     * 异步执行提交操作
     *
     * @param commitTask 提交任务
     * @return CompletableFuture
     */
    public CompletableFuture<Void> executeCommitAsync(Runnable commitTask) {
        return executeWithSpringTransactionContext(commitTask, commitExecutor);
    }

    /**
     * 异步执行回滚操作
     *
     * @param rollbackTask 回滚任务
     * @return CompletableFuture
     */
    public CompletableFuture<Void> executeRollbackAsync(Runnable rollbackTask) {
        return executeWithSpringTransactionContext(rollbackTask, rollbackExecutor);
    }

    /**
     * 异步执行提交操作（带返回值）
     *
     * @param commitTask 提交任务
     * @return CompletableFuture
     */
    public <T> CompletableFuture<T> executeCommitAsync(Supplier<T> commitTask) {
        return executeWithTransactionContext(commitTask, commitExecutor);
    }

    /**
     * 异步执行回滚操作（带返回值）
     *
     * @param rollbackTask 回滚任务
     * @return CompletableFuture
     */
    public <T> CompletableFuture<T> executeRollbackAsync(Supplier<T> rollbackTask) {
        return executeWithTransactionContext(rollbackTask, rollbackExecutor);
    }

    /**
     * 批量异步执行提交操作
     *
     * @param commitTasks    提交任务列表
     * @param timeoutSeconds 超时时间（秒）
     * @return 执行结果，包含成功和失败信息
     */
    public AsyncExecutionResult executeBatchCommitAsync(java.util.List<Runnable> commitTasks, long timeoutSeconds) {
        return executeBatchAsyncWithSpringTransactionContext(commitTasks, commitExecutor, timeoutSeconds, "提交");
    }

    /**
     * 批量异步执行回滚操作
     *
     * @param rollbackTasks  回滚任务列表
     * @param timeoutSeconds 超时时间（秒）
     * @return 执行结果，包含成功和失败信息
     */
    public AsyncExecutionResult executeBatchRollbackAsync(java.util.List<Runnable> rollbackTasks, long timeoutSeconds) {
        return executeBatchAsyncWithSpringTransactionContext(rollbackTasks, rollbackExecutor, timeoutSeconds, "回滚");
    }

    /**
     * 带Spring事务上下文传递的异步执行（无返回值）
     * 核心思路：通过TransactionSynchronizationManager手动传递Spring事务的数据库连接
     */
    private CompletableFuture<Void> executeWithSpringTransactionContext(Runnable task, ExecutorService executor) {
        // 捕获当前线程的Spring事务资源（数据库连接等）
        Map<Object, Object> resourceMap = null;
        TransactionContext currentContext = null;

        try {
            // 获取当前线程的事务资源
            if (TransactionSynchronizationManager.isActualTransactionActive()) {
                resourceMap = TransactionSynchronizationManager.getResourceMap();
                log.debug("[{}|{}] [DEBUG] - 捕获Spring事务上下文 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("资源数量", resourceMap.size()));
            }

            // 捕获自定义事务上下文
            currentContext = TransactionContextHolder.getCurrentContext();
        } catch (Exception e) {
            log.warn("[{}|{}] [WARN] - 捕获Spring事务上下文失败 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    LoggingUtils.formatException(e));
        }

        // 为了在lambda中使用，需要final变量
        final Map<Object, Object> finalResourceMap = resourceMap;
        final TransactionContext finalCurrentContext = currentContext;

        return CompletableFuture.runAsync(() -> {
            try {
                // 在异步线程中恢复Spring事务上下文
                if (finalResourceMap != null && !finalResourceMap.isEmpty()) {
                    // 绑定事务资源到当前异步线程
                    finalResourceMap.forEach(TransactionSynchronizationManager::bindResource);

                    log.debug("[{}|{}] [DEBUG] - 异步线程中恢复Spring事务上下文成功 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("资源数量", finalResourceMap.size()));
                }

                // 恢复自定义事务上下文
                if (finalCurrentContext != null) {
                    TransactionContextHolder.setCurrentContext(finalCurrentContext);
                }

                // 执行业务任务
                task.run();

            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 异步任务执行失败 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatException(e), e);
                throw new RuntimeException("异步任务执行失败", e);
            } finally {
                // 清理异步线程中的事务上下文
                try {
                    if (finalResourceMap != null && !finalResourceMap.isEmpty()) {
                        // 解绑事务资源
                        finalResourceMap.keySet().forEach(key -> {
                            try {
                                TransactionSynchronizationManager.unbindResourceIfPossible(key);
                            } catch (Exception e) {
                                log.warn("[{}|{}] [WARN] - 解绑事务资源失败 {}",
                                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                                        LoggingUtils.formatContext("资源", key.toString()));
                            }
                        });
                    }

                    // 清理自定义事务上下文
                    if (finalCurrentContext != null) {
                        TransactionContextHolder.clearCurrentContext();
                    }
                } catch (Exception e) {
                    log.warn("[{}|{}] [WARN] - 清理异步线程事务上下文失败 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatException(e));
                }
            }
        }, executor);
    }

    /**
     * 批量异步执行任务的通用方法（带Spring事务上下文传递）
     */
    private AsyncExecutionResult executeBatchAsyncWithSpringTransactionContext(java.util.List<Runnable> tasks,
                                                                               ExecutorService executor,
                                                                               long timeoutSeconds,
                                                                               String operationType) {
        if (tasks == null || tasks.isEmpty()) {
            return new AsyncExecutionResult(0, 0, java.util.Collections.emptyList());
        }

        java.util.List<CompletableFuture<Void>> futures = tasks.stream()
                .map(task -> executeWithSpringTransactionContext(task, executor))
                .collect(java.util.stream.Collectors.toList());

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("[{}|{}] [ERROR] - 异步{}操作超时 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    operationType, LoggingUtils.formatContext("timeout", timeoutSeconds + "秒"));
        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 异步{}操作异常 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    operationType, LoggingUtils.formatException(e), e);
        }

        // 统计结果
        int successCount = 0;
        java.util.List<String> failures = new java.util.ArrayList<>();

        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<Void> future = futures.get(i);
            if (future.isDone() && !future.isCompletedExceptionally()) {
                successCount++;
            } else if (future.isCompletedExceptionally()) {
                try {
                    future.get();
                } catch (Exception ex) {
                    String errorMsg = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();
                    failures.add("任务" + i + ": " + errorMsg);
                }
            } else {
                failures.add("任务" + i + ": 超时未完成");
            }
        }

        return new AsyncExecutionResult(successCount, tasks.size() - successCount, failures);
    }

    /**
     * 批量异步执行任务的通用方法（带事务上下文传递）
     */
    private AsyncExecutionResult executeBatchAsyncWithContext(java.util.List<Runnable> tasks,
                                                              ExecutorService executor,
                                                              long timeoutSeconds,
                                                              String operationType) {
        if (tasks == null || tasks.isEmpty()) {
            return new AsyncExecutionResult(0, 0, java.util.Collections.emptyList());
        }

        java.util.List<CompletableFuture<Void>> futures = tasks.stream()
                .map(task -> CompletableFuture.runAsync(() -> {
                    try {
                        task.run();
                    } catch (Exception e) {
                        log.error("[{}|{}] [ERROR] - 异步{}任务执行失败 {}",
                                LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                                operationType, LoggingUtils.formatException(e), e);
                        throw new RuntimeException(operationType + "失败: " + e.getMessage(), e);
                    }
                }, executor))
                .collect(java.util.stream.Collectors.toList());

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("[{}|{}] [ERROR] - 异步{}操作超时 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    operationType, LoggingUtils.formatContext("timeout", timeoutSeconds + "秒"));
        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 异步{}操作异常 {}",
                    LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                    operationType, LoggingUtils.formatException(e), e);
        }

        // 统计结果
        int successCount = 0;
        java.util.List<String> failures = new java.util.ArrayList<>();

        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<Void> future = futures.get(i);
            if (future.isDone() && !future.isCompletedExceptionally()) {
                successCount++;
            } else if (future.isCompletedExceptionally()) {
                try {
                    future.get();
                } catch (Exception ex) {
                    String errorMsg = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();
                    failures.add("任务" + i + ": " + errorMsg);
                }
            } else {
                failures.add("任务" + i + ": 超时未完成");
            }
        }

        return new AsyncExecutionResult(successCount, tasks.size() - successCount, failures);
    }

    /**
     * 获取线程池状态信息
     */
    public String getExecutorStatus() {
        ThreadPoolExecutor commitPool = (ThreadPoolExecutor) commitExecutor;
        ThreadPoolExecutor rollbackPool = (ThreadPoolExecutor) rollbackExecutor;

        return String.format("提交线程池: 活跃=%d, 队列=%d, 完成=%d; 回滚线程池: 活跃=%d, 队列=%d, 完成=%d",
                commitPool.getActiveCount(), commitPool.getQueue().size(), commitPool.getCompletedTaskCount(),
                rollbackPool.getActiveCount(), rollbackPool.getQueue().size(), rollbackPool.getCompletedTaskCount());
    }

    /**
     * 销毁异步事务执行器
     * 注意：线程池的实际关闭由 TransactionConnectionPoolManager 负责
     */
    @PreDestroy
    public void destroy() {
        log.debug("[{}|{}] [DEBUG] - 异步事务执行器销毁完成 {}",
                LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                LoggingUtils.formatContext("说明", "线程池由 TransactionConnectionPoolManager 统一管理"));
    }

    /**
     * 异步执行结果
     */
    public static class AsyncExecutionResult {
        private final int successCount;
        private final int failureCount;
        private final java.util.List<String> failures;

        public AsyncExecutionResult(int successCount, int failureCount, java.util.List<String> failures) {
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.failures = failures;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public java.util.List<String> getFailures() {
            return failures;
        }

        public boolean hasFailures() {
            return failureCount > 0;
        }

        public boolean isAllSuccess() {
            return failureCount == 0;
        }

        @Override
        public String toString() {
            return String.format("AsyncExecutionResult{成功=%d, 失败=%d, 失败详情=%s}",
                    successCount, failureCount, failures);
        }
    }
}
